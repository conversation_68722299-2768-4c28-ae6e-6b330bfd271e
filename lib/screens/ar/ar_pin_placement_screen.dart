import 'dart:async';
import 'dart:async' show unawaited;
import 'dart:convert';
import 'dart:math' as math;
import 'dart:typed_data';
import 'dart:ui' show ImageFilter; // Specifically import ImageFilter
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:arkit_plugin/arkit_plugin.dart';
import 'package:vector_math/vector_math_64.dart' as vector;
import 'package:provider/provider.dart';
import 'package:dio/dio.dart';
import 'package:geocoding/geocoding.dart';
import 'package:geolocator/geolocator.dart';
import 'package:torch_light/torch_light.dart';
import 'package:image/image.dart' as img;
import '../../models/pin_skin.dart';
import '../../models/music_track.dart';
import '../../providers/auth_provider.dart';
import '../../providers/skin_provider.dart';
import '../../widgets/common/gradient_button.dart';
import '../../config/constants.dart';
import '../../providers/map_provider.dart';
import '../../providers/user_provider.dart';
import '../../widgets/bottomsheets/caption_input_bottomsheet.dart';
import '../../services/gamification_integration_service.dart';
import '../../providers/pin_provider.dart';
import '../../utils/location_utils.dart';
import '../../services/music/genre_lookup_service.dart';
import '../../services/music/spotify_service.dart';
import 'package:flutter/rendering.dart';

class ARPinPlacementScreen extends StatefulWidget {
  final double? initialLatitude;
  final double? initialLongitude;
  final MusicTrack? selectedTrack;
  
  const ARPinPlacementScreen({
    Key? key,
    this.initialLatitude,
    this.initialLongitude,
    required this.selectedTrack,
  }) : super(key: key);

  @override
  State<ARPinPlacementScreen> createState() => _ARPinPlacementScreenState();
}

class _ARPinPlacementScreenState extends State<ARPinPlacementScreen> 
    with TickerProviderStateMixin {
  // ARKit Controller
  late ARKitController arkitController;
  
  // AR State for proper plane tracking
  Map<String, ARKitPlaneAnchor> detectedPlaneAnchors = {};
  Map<String, bool> planeOrientations = {}; // true = horizontal/floor, false = vertical/wall
  
  // Position tracking
  Position? _currentPosition;
  
  // Pin placement state
  ARKitNode? pinNode;
  ARKitNode? radiusNode;
  ARKitNode? _headNode;
  vector.Vector3? pinPosition;
  bool _hasPlacedPin = false;
  bool _isPlacingPin = false;
  bool _arInitialized = false;
  
  // Camera flash state
  bool _isFlashOn = false;
  
  // UI State
  double _currentRadius = 50.0; // Default 50 meters
  int _selectedPinSkinIndex = 0;
  
  // Genre lookup state
  late GenreLookupService _genreLookupService;
  String? _cachedGenre;
  bool _isLookingUpGenre = false;
  
  // Animation Controllers
  late AnimationController _carouselAnimationController;
  late AnimationController _radiusAnimationController;
  late AnimationController _pulseAnimationController;
  late AnimationController _sonarAnimationController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _sonarAnimation;
  Timer? _sonarTimer;
  
  // Sonar animation state
  List<ARKitNode> _sonarRings = [];
  int _currentSonarRing = 0;
  
  // Pin image URL
  static const String _pinImageUrl = 'https://ih1.redbubble.net/image.736561836.1662/flat,750x,075,f-pad,750x1000,f8f8f8.u1.jpg';
  
  // Get unlocked skins from SkinProvider (only those with valid images)
  List<PinSkin> get _availableSkins {
    final skinProvider = Provider.of<SkinProvider>(context, listen: false);
    final unlockedSkins = skinProvider.unlockedSkins;
    
    // Detailed logging
    debugPrint('AR Screen - Total Unlocked Skins: ${unlockedSkins.length}');
    for (final skin in unlockedSkins) {
      debugPrint('  - ${skin.id}: ${skin.name} (locked: ${skin.locked}, unlocked: ${skin.isUnlocked}, image: ${skin.image})');
    }
    
    // Filter skins to only include those that are TRULY unlocked and have valid images
    final validSkins = unlockedSkins.where((skin) => 
      skin.isUnlocked == true && 
      !skin.locked && 
      _hasValidImageUrl(skin.image)
    ).toList();
    
    debugPrint('AR Screen - Strictly Valid Unlocked Skins: ${validSkins.length}');
    
    // If no valid unlocked skins, provide default skin
    if (validSkins.isEmpty) {
      return [
        PinSkin(
          id: 1,
          name: "Default",
          image: "https://i.imgur.com/pin-default.png", // Using a web URL for consistency
          description: "Classic pin design",
          createdAt: DateTime.now(),
          isUnlocked: true,
          locked: false,
        ),
      ];
    }
    
    return validSkins;
  }

  // Helper to check if image URL is valid
  bool _hasValidImageUrl(String? imageUrl) {
    if (imageUrl == null || imageUrl.isEmpty) return false;
    
    // Check if it's a valid HTTP/HTTPS URL or a valid asset path
    if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
      return true;
    }
    
    // Check if it's a valid asset path
    if (imageUrl.startsWith('assets/')) {
      return true;
    }
    
    return false;
  }

  /// Converts AR coordinates to real-world offset in meters
  /// Returns a Vector2 where x is east-west offset and y is north-south offset
  vector.Vector2 arPositionToRealWorldOffset(vector.Vector3 arPosition) {
    // ARKit uses meters, so we can use the x and z coordinates directly
    // x = east-west offset (positive is east)
    // z = north-south offset (negative is north in ARKit)
    
    // Apply a maximum offset limit for safety (50 meters)
    const double maxOffset = 50.0;
    
    double offsetX = arPosition.x;
    double offsetZ = -arPosition.z; // Negative because ARKit's Z is inverted
    
    // Calculate total offset distance
    double totalOffset = math.sqrt(offsetX * offsetX + offsetZ * offsetZ);
    
    // If total offset exceeds max, scale it down
    if (totalOffset > maxOffset) {
      double scale = maxOffset / totalOffset;
      offsetX *= scale;
      offsetZ *= scale;
      
      debugPrint('⚠️ AR: Pin placement distance exceeded ${maxOffset}m, scaled down from ${totalOffset.toStringAsFixed(2)}m');
    }
    
    debugPrint('📐 AR: Offset calculation - X(E-W): ${offsetX.toStringAsFixed(2)}m, Z(N-S): ${offsetZ.toStringAsFixed(2)}m');
    
    return vector.Vector2(offsetX, offsetZ);
  }

  /// Calculates new GPS coordinates based on initial position and offset in meters
  /// Uses accurate geodesic calculations accounting for Earth's curvature
  Map<String, double> calculateOffsetCoordinates(
      double initialLat, 
      double initialLng, 
      vector.Vector2 offsetMeters) {
    // Earth's radius in meters (WGS84 mean radius)
    const double earthRadius = 6371000;
    
    // Convert latitude from degrees to radians
    final latRad = initialLat * (math.pi / 180);
    
    // Calculate new latitude
    // Distance along meridian: 1 degree latitude = ~111,320 meters
    final newLat = initialLat + (offsetMeters.y / earthRadius) * (180 / math.pi);
    
    // Calculate new longitude
    // Distance along parallel depends on latitude: 1 degree longitude = ~111,320 * cos(latitude) meters
    // We use the average latitude between initial and new for better accuracy
    final avgLatRad = ((initialLat + newLat) / 2) * (math.pi / 180);
    final newLng = initialLng + 
        (offsetMeters.x / (earthRadius * math.cos(avgLatRad))) * (180 / math.pi);
    
    debugPrint('🌍 GPS: Initial (${initialLat.toStringAsFixed(6)}, ${initialLng.toStringAsFixed(6)})');
    debugPrint('🌍 GPS: New (${newLat.toStringAsFixed(6)}, ${newLng.toStringAsFixed(6)})');
    debugPrint('🌍 GPS: Offset - E-W: ${offsetMeters.x.toStringAsFixed(2)}m, N-S: ${offsetMeters.y.toStringAsFixed(2)}m');
    
    return {
      'latitude': newLat,
      'longitude': newLng,
    };
  }

  /// Validates that the calculated coordinates are reasonable
  bool validateCoordinates(double lat, double lng, double initialLat, double initialLng) {
    // Check if coordinates are valid
    if (lat < -90 || lat > 90 || lng < -180 || lng > 180) {
      debugPrint('❌ GPS: Invalid coordinates - lat: $lat, lng: $lng');
      return false;
    }
    
    // Calculate distance from initial position
    final distance = calculateDistance(initialLat, initialLng, lat, lng);
    
    // Warn if distance is unusually large (> 100m)
    if (distance > 100) {
      debugPrint('⚠️ GPS: Pin placement distance is ${distance.toStringAsFixed(2)}m from user position');
    }
    
    return true;
  }

  /// Test method to verify coordinate calculations
  void _testCoordinateCalculations() {
    debugPrint('🧪 Testing coordinate calculations...');
    
    // Test case 1: 10m east
    final testOffset1 = vector.Vector2(10.0, 0.0);
    final testLat = 37.7749; // San Francisco
    final testLng = -122.4194;
    
    final result1 = calculateOffsetCoordinates(testLat, testLng, testOffset1);
    final distance1 = calculateDistance(testLat, testLng, result1['latitude']!, result1['longitude']!);
    
    debugPrint('Test 1 - 10m East:');
    debugPrint('  Expected: ~10m, Actual: ${distance1.toStringAsFixed(2)}m');
    debugPrint('  Coordinates: (${result1['latitude']!.toStringAsFixed(6)}, ${result1['longitude']!.toStringAsFixed(6)})');
    
    // Test case 2: 10m north
    final testOffset2 = vector.Vector2(0.0, 10.0);
    final result2 = calculateOffsetCoordinates(testLat, testLng, testOffset2);
    final distance2 = calculateDistance(testLat, testLng, result2['latitude']!, result2['longitude']!);
    
    debugPrint('Test 2 - 10m North:');
    debugPrint('  Expected: ~10m, Actual: ${distance2.toStringAsFixed(2)}m');
    debugPrint('  Coordinates: (${result2['latitude']!.toStringAsFixed(6)}, ${result2['longitude']!.toStringAsFixed(6)})');
    
    // Test case 3: 10m northeast (diagonal)
    final testOffset3 = vector.Vector2(7.07, 7.07); // ~10m diagonal
    final result3 = calculateOffsetCoordinates(testLat, testLng, testOffset3);
    final distance3 = calculateDistance(testLat, testLng, result3['latitude']!, result3['longitude']!);
    
    debugPrint('Test 3 - ~10m Northeast:');
    debugPrint('  Expected: ~10m, Actual: ${distance3.toStringAsFixed(2)}m');
    debugPrint('  Coordinates: (${result3['latitude']!.toStringAsFixed(6)}, ${result3['longitude']!.toStringAsFixed(6)})');
    
    debugPrint('🧪 Coordinate calculation tests completed');
  }

  @override
  void initState() {
    super.initState();
    
    // Initialize genre lookup service
    final spotifyService = SpotifyService();
    _genreLookupService = GenreLookupService(spotifyService);
    
    // Initialize animations
    _pulseAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(begin: 0.8, end: 1.2).animate(
      CurvedAnimation(parent: _pulseAnimationController, curve: Curves.easeInOut),
    );
    _pulseAnimationController.repeat(reverse: true);
    
    // Initialize animation controllers
    _carouselAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _sonarAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _sonarAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _sonarAnimationController, curve: Curves.easeOut),
    );
    
    _radiusAnimationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    // Start animations
    _carouselAnimationController.forward();
    
    // Load skin data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadSkinData();
      _checkDuplicatePin(); // Call the new duplicate pin check
      _checkPinLimit(); // Call the new pin limit check
      // Get initial position with altitude
      _getCurrentPosition();
      
      // Start background genre lookup
      _startBackgroundGenreLookup();
      
      // Test coordinate calculations (development only)
      _testCoordinateCalculations();
    });

    // Debug logging for available and unlocked skins
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final skinProvider = Provider.of<SkinProvider>(context, listen: false);
      final availableSkins = skinProvider.availableSkins;
      final unlockedSkins = skinProvider.unlockedSkins;
      debugPrint('Available Skins (${availableSkins.length}):');
      for (final skin in availableSkins) {
        debugPrint('  - ${skin.id}: ${skin.name} (locked: ${skin.locked}, unlocked: ${skin.isUnlocked})');
      }
      debugPrint('Unlocked Skins (${unlockedSkins.length}):');
      for (final skin in unlockedSkins) {
        debugPrint('  - ${skin.id}: ${skin.name}');
      }
    });
    
    
  }

  Future<void> _getCurrentPosition() async {
    debugPrint('AR Screen - Getting current altitude...');
    try {
      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.best,
        forceAndroidLocationManager: false,
      );
      setState(() {
        _currentPosition = position;
      });
      debugPrint('📍 Current altitude: ${position.altitude} meters');
    } catch (e) {
      debugPrint('Error getting position: $e');
    }
  }
  
  /// Start background genre lookup for the selected track
  Future<void> _startBackgroundGenreLookup() async {
    if (widget.selectedTrack?.artist == null || widget.selectedTrack!.artist.isEmpty) {
      debugPrint('🎵 [AR] No artist available for genre lookup');
      return;
    }
    
    setState(() {
      _isLookingUpGenre = true;
    });
    
    try {
      debugPrint('🎵 [AR] Starting background genre lookup for: ${widget.selectedTrack!.artist}');
      
      // Extract main artist and lookup genre
      final mainArtist = _genreLookupService.extractMainArtist(widget.selectedTrack!.artist);
      final genre = await _genreLookupService.getArtistGenre(mainArtist);
      
      if (mounted) {
        setState(() {
          _cachedGenre = genre;
          _isLookingUpGenre = false;
        });
        
        debugPrint('🎯 [AR] Genre lookup completed: ${genre ?? "not found"} for "$mainArtist"');
      }
    } catch (e) {
      debugPrint('⚠️ [AR] Genre lookup failed: $e');
      if (mounted) {
        setState(() {
          _isLookingUpGenre = false;
        });
      }
    }
  }

  Future<void> _loadSkinData() async {
    final skinProvider = Provider.of<SkinProvider>(context, listen: false);
    await skinProvider.refreshSkins();
  }
  
  // New method to check for duplicate pins
  Future<void> _checkDuplicatePin() async {
    if (widget.selectedTrack == null || widget.initialLatitude == null || widget.initialLongitude == null) {
      debugPrint('AR Screen - Skipping duplicate pin check: selectedTrack or location is null.');
      return;
    }

    final pinProvider = Provider.of<PinProvider>(context, listen: false);
    final currentLatitude = widget.initialLatitude!;
    final currentLongitude = widget.initialLongitude!;
    const checkRadius = 100.0; // 100 meters

    debugPrint('AR Screen - Checking for duplicate pins within ${checkRadius}m...');
    await pinProvider.loadNearbyPins(currentLatitude, currentLongitude, checkRadius);

    final nearbyPins = pinProvider.nearbyPins;

    for (final pin in nearbyPins) {
      // Check for same song title and artist
      final isSameSong = pin.trackTitle == widget.selectedTrack!.title &&
                         pin.trackArtist == widget.selectedTrack!.artist;
      
      // Calculate distance between current location and nearby pin
      final distance = calculateDistance(
        currentLatitude, currentLongitude,
        pin.latitude, pin.longitude,
      );
      
      debugPrint('  - Pin ID: ${pin.id}, Title: ${pin.trackTitle}, Artist: ${pin.trackArtist}, Distance: ${distance.toStringAsFixed(2)}m, Is Same Song: $isSameSong');

      if (isSameSong && distance <= checkRadius) {
        debugPrint('AR Screen - Duplicate pin found! Showing dialog.');
        // Show a dialog that the pin cannot be placed
        if (mounted) {
          showDialog(
            context: context,
            barrierDismissible: false,
            barrierColor: Colors.black.withOpacity(0.6),
            builder: (BuildContext dialogContext) {
              return Dialog(
                backgroundColor: Colors.transparent,
                insetPadding: const EdgeInsets.all(20),
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20),
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Colors.white.withOpacity(0.2),
                        Colors.white.withOpacity(0.1),
                      ],
                    ),
                    border: Border.all(
                      color: Colors.white.withOpacity(0.3),
                      width: 1.5,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.2),
                        blurRadius: 20,
                        offset: const Offset(0, 10),
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(20),
                    child: BackdropFilter(
                      filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                      child: Container(
                        padding: const EdgeInsets.all(24),
                        decoration: BoxDecoration(
                          color: Colors.transparent,
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            // Warning Icon with Animated Pulse
                            TweenAnimationBuilder<double>(
                              duration: const Duration(milliseconds: 800),
                              tween: Tween(begin: 1.0, end: 1.2),
                              curve: Curves.easeInOut,
                              builder: (context, scale, child) {
                                return Transform.scale(
                                  scale: scale,
                                  child: Icon(
                                    Icons.warning_rounded,
                                    color: Colors.orange.shade600,
                                    size: 64,
                                  ),
                                );
                              },
                            ),
                            const SizedBox(height: 16),
                            
                            // Title
                            Text(
                              'Duplicate Pin Detected',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 22,
                                fontWeight: FontWeight.w700,
                                letterSpacing: 0.5,
                                shadows: [
                                  Shadow(
                                    blurRadius: 10.0,
                                    color: Colors.black.withOpacity(0.3),
                                    offset: const Offset(0, 3),
                                  ),
                                ],
                              ),
                              textAlign: TextAlign.center,
                            ),
                            const SizedBox(height: 16),
                            
                            // Message
                            Text(
                              'You cannot drop this song here. A pin for \'${widget.selectedTrack!.title}\' by ${widget.selectedTrack!.artist} already exists within ${checkRadius.round()} meters.',
                              style: TextStyle(
                                color: Colors.white.withOpacity(0.9),
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                                height: 1.5,
                              ),
                              textAlign: TextAlign.center,
                            ),
                            const SizedBox(height: 24),
                            
                            // Go Back Button
                            ElevatedButton(
                              onPressed: () {
                                Navigator.of(dialogContext).pop(); // Dismiss dialog
                                Navigator.pop(context); // Go back to previous screen
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Theme.of(context).colorScheme.primary.withOpacity(0.8),
                                padding: const EdgeInsets.symmetric(horizontal: 40, vertical: 12),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(30),
                                ),
                                elevation: 5,
                              ),
                              child: const Text(
                                'Go Back',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              );
            },
          );
        }
        return; // Exit if duplicate found
      }
    }
    debugPrint('AR Screen - No duplicate pins found within ${checkRadius}m.');
  }
  
  // New method to check for pin limit
  Future<void> _checkPinLimit() async {
    final pinProvider = Provider.of<PinProvider>(context, listen: false);
    await pinProvider.loadUserPins(); // Ensure user pins are loaded

    final userPins = pinProvider.userPins;
    debugPrint('AR Screen - Total user pins loaded: ${userPins.length}');

    final now = DateTime.now();
    final twentyFourHoursAgo = now.subtract(const Duration(hours: 24));

    final recentPins = userPins.where((pin) =>
      pin.createdAt.isAfter(twentyFourHoursAgo)
    ).toList();
    
    final recentPinsCount = recentPins.length;

    debugPrint('AR Screen - User has $recentPinsCount pins in the last 24 hours.');

    if (recentPinsCount >= 2) {
      // Sort recent pins by creation time ascending to find the 2nd oldest (or last if only 2)
      final sortedRecentPins = recentPins..sort((a, b) => a.createdAt.compareTo(b.createdAt));
      
      debugPrint('AR Screen - Sorted recent pins count: ${sortedRecentPins.length}');
      if (sortedRecentPins.isNotEmpty) {
        debugPrint('  Earliest recent pin created at: ${sortedRecentPins.first.createdAt}');
      }
      if (sortedRecentPins.length >= 2) {
        debugPrint('  Second earliest recent pin created at: ${sortedRecentPins[1].createdAt}');
      }
      
      DateTime? nextAvailablePinTime;
      // We need the oldest pin within the last 24 hours, not necessarily the 2nd oldest overall.
      // If there are exactly 2 pins, we take the creation time of the first one in the sorted list.
      // If there are more than 2, we still take the first one, as its expiry dictates the next available slot.
      if (sortedRecentPins.isNotEmpty) { // Should always be true if recentPinsCount >= 2
        nextAvailablePinTime = sortedRecentPins.first.createdAt.add(const Duration(hours: 24));
      }
      
      debugPrint('AR Screen - Pin limit reached! Showing dialog. Next available: $nextAvailablePinTime');
      if (mounted) {
        showDialog(
          context: context,
          barrierDismissible: false,
          barrierColor: Colors.black.withOpacity(0.6),
          builder: (BuildContext dialogContext) {
            return PinLimitReachedDialog(nextAvailableTime: nextAvailablePinTime);
          },
        );
      }
      return; // Exit if limit reached
    }
  }
  
  @override
  void dispose() {
    _sonarTimer?.cancel();
    _clearSonarRings(); // Clean up sonar rings
    _carouselAnimationController.dispose();
    _radiusAnimationController.dispose();
    _pulseAnimationController.dispose();
    _sonarAnimationController.dispose();
    
    // Clean up AR session with error handling to prevent plugin exceptions
    try {
      arkitController.dispose();
      debugPrint('✅ [AR-CLEANUP] ARKit controller disposed successfully');
    } catch (e) {
      debugPrint('⚠️ [AR-CLEANUP] ARKit disposal error (non-critical): $e');
      // Continue with disposal - this error shouldn't block the AR → Map flow
    }
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Consumer<SkinProvider>(
      builder: (context, skinProvider, child) {
        return Scaffold(
      backgroundColor: Colors.black,
      extendBodyBehindAppBar: true,
      resizeToAvoidBottomInset: false,
      body: Stack(
        children: [
          // ARKit View with both horizontal and vertical plane detection
          ARKitSceneView(
            onARKitViewCreated: _onARKitViewCreated,
            planeDetection: ARPlaneDetection.horizontalAndVertical,
            enableTapRecognizer: true,
            showStatistics: false,
            showWorldOrigin: false,
          ),
          
          // Dark gradient overlay
          IgnorePointer(
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.black.withOpacity(0.6),
                    Colors.transparent,
                    Colors.transparent,
                    Colors.black.withOpacity(0.8),
                  ],
                  stops: const [0.0, 0.2, 0.7, 1.0],
                ),
              ),
            ),
          ),
          
          // Top Bar with Track Info
          SafeArea(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildTopBar(context, theme),
                
                // Selected Track Info
                if (widget.selectedTrack != null)
                  Container(
                    margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.7),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: theme.colorScheme.primary.withOpacity(0.3),
                        width: 1,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.2),
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Row(
                      children: [
                        // Album Art
                        Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                            image: widget.selectedTrack!.albumArt != null
                                ? DecorationImage(
                                    image: CachedNetworkImageProvider(widget.selectedTrack!.albumArt!),
                                    fit: BoxFit.cover,
                                  )
                                : null,
                            color: theme.colorScheme.primary.withOpacity(0.2),
                          ),
                          child: widget.selectedTrack!.albumArt == null
                              ? Icon(
                                  Icons.music_note,
                                  color: theme.colorScheme.primary,
                                  size: 24,
                                )
                              : null,
                        ),
                        const SizedBox(width: 12),
                        // Track Info
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                widget.selectedTrack!.title,
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 14,
                                  fontWeight: FontWeight.w600,
                                ),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                              const SizedBox(height: 2),
                              Text(
                                widget.selectedTrack!.artist,
                                style: TextStyle(
                                  color: Colors.white.withOpacity(0.7),
                                  fontSize: 12,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],
                          ),
                        ),
                        // Duration
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: theme.colorScheme.primary.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            widget.selectedTrack!.formattedDuration,
                            style: TextStyle(
                              color: theme.colorScheme.primary,
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          ),
          
          // Persistent Hint Overlay
          if (!_hasPlacedPin && _arInitialized)
            Positioned(
              bottom: MediaQuery.of(context).size.height * 0.35,
              left: 20,
              right: 20,
              child: AnimatedOpacity(
                duration: const Duration(milliseconds: 300),
                opacity: _arInitialized ? 1.0 : 0.0,
                child: IgnorePointer(
                  ignoring: true,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.6),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: theme.colorScheme.primary.withOpacity(0.3),
                        width: 1,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.2),
                          blurRadius: 10,
                          offset: const Offset(0, 5),
                        ),
                      ],
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Status Icons
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            // Floor Detection Status
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                              decoration: BoxDecoration(
                                color: planeOrientations.values.where((isFloor) => isFloor).isNotEmpty
                                    ? Colors.green.withOpacity(0.2)
                                    : Colors.orange.withOpacity(0.2),
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: planeOrientations.values.where((isFloor) => isFloor).isNotEmpty
                                      ? Colors.green
                                      : Colors.orange,
                                  width: 1,
                                ),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    planeOrientations.values.where((isFloor) => isFloor).isNotEmpty
                                        ? Icons.check_circle
                                        : Icons.search,
                                    color: planeOrientations.values.where((isFloor) => isFloor).isNotEmpty
                                        ? Colors.green
                                        : Colors.orange,
                                    size: 16,
                                  ),
                                  const SizedBox(width: 6),
                                  Text(
                                    planeOrientations.values.where((isFloor) => isFloor).isNotEmpty
                                        ? 'Floor Detected'
                                        : 'Scanning Floor',
                                    style: TextStyle(
                                      color: planeOrientations.values.where((isFloor) => isFloor).isNotEmpty
                                          ? Colors.green
                                          : Colors.orange,
                                      fontSize: 12,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        // Instruction Text
                        Text(
                          planeOrientations.values.where((isFloor) => isFloor).isNotEmpty
                              ? 'Tap anywhere on the floor to place your pin'
                              : 'Move your device slowly to scan the floor',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          planeOrientations.values.where((isFloor) => isFloor).isNotEmpty
                              ? 'Make sure to place it where others can easily find it!'
                              : 'Point your camera down at the floor surface',
                          style: TextStyle(
                            color: Colors.white.withOpacity(0.7),
                            fontSize: 14,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          
          // Bottom UI elements
          Positioned(
            left: 0,
            right: 0,
            bottom: 0,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Pin Skin Carousel
                if (!_hasPlacedPin || _isPlacingPin)
                  _buildPinSkinCarousel(context, theme),
                
                // Radius Adjustment UI
                if (_hasPlacedPin && !_isPlacingPin)
                  _buildRadiusAdjustment(context, theme),
                
                // Pin placement info
                // if (_hasPlacedPin && !_isPlacingPin)
                //   _buildPinPlacementInfo(context, theme),
                
                // Action Buttons
                if (_hasPlacedPin && !_isPlacingPin)
                  _buildActionButtons(context, theme),
                
                // Safe area padding
                SizedBox(height: MediaQuery.of(context).padding.bottom),
              ],
            ),
          ),
          
          // Loading overlay
          if (!_arInitialized)
            Container(
              color: Colors.black.withOpacity(0.8),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(
                        Theme.of(context).colorScheme.primary,
                      ),
                    ),
                    const SizedBox(height: 24),
                    const Text(
                      'Initializing AR...',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Please grant camera permission if prompted',
                      style: TextStyle(
                        color: Colors.white70,
                        fontSize: 14,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          
          // Saving overlay
          // Note: Removed - now using optimistic pin dropping
        ],
      ),
    );
      },
    );
  }
  
  Widget _buildTopBar(BuildContext context, ThemeData theme) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      child: Row(
        children: [
          // Back button
          Container(
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.5),
              shape: BoxShape.circle,
            ),
            child: IconButton(
              icon: const Icon(Icons.arrow_back_ios_new, color: Colors.white),
              onPressed: () => Navigator.pop(context),
            ),
          ),
          
          const Spacer(),
          
          // Title
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.5),
              borderRadius: BorderRadius.circular(25),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.place_outlined,
                  color: theme.colorScheme.primary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                const Text(
                  'Drop a Pin',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
          
          const Spacer(),
          
          // Flash toggle
          Container(
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.5),
              shape: BoxShape.circle,
            ),
            child: IconButton(
              icon: Icon(
                _isFlashOn ? Icons.flash_on : Icons.flash_off,
                color: _isFlashOn ? Colors.amber : Colors.white,
              ),
              tooltip: _isFlashOn ? 'Turn Flash Off' : 'Turn Flash On',
              onPressed: _toggleFlash,
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildPinSkinCarousel(BuildContext context, ThemeData theme) {
    final isDark = theme.brightness == Brightness.dark;
    
    return Container(
      margin: const EdgeInsets.only(bottom: 20),
      child: FadeTransition(
        opacity: _carouselAnimationController,
        child: Column(
          children: [
            // Carousel title with glassmorphism
            Container(
              margin: const EdgeInsets.only(bottom: 16),
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.white.withOpacity(isDark ? 0.1 : 0.2),
                    Colors.white.withOpacity(isDark ? 0.05 : 0.1),
                  ],
                ),
                borderRadius: BorderRadius.circular(25),
                border: Border.all(
                  color: Colors.white.withOpacity(isDark ? 0.2 : 0.3),
                  width: 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(isDark ? 0.3 : 0.1),
                    blurRadius: 15,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: const Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.palette_outlined,
                    color: Colors.white,
                    size: 18,
                  ),
                  SizedBox(width: 8),
                  Text(
                    'Choose a Pin Style',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      letterSpacing: 0.5,
                    ),
                  ),
                ],
              ),
            ),
            
            // Pin skins carousel with glassmorphism cards
            SizedBox(
              height: 164,
              child: Consumer<SkinProvider>(
                builder: (context, skinProvider, child) {
                  final skins = _availableSkins;
                  
                  return ListView.builder(
                    scrollDirection: Axis.horizontal,
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    itemCount: skins.length,
                    itemBuilder: (context, index) {
                      final pinSkin = skins[index];
                      final isSelected = index == _selectedPinSkinIndex;
                      final gradientColors = _getCardGradient(index, isDark);
                      
                      return GestureDetector(
                        onTap: () {
                          setState(() {
                            _selectedPinSkinIndex = index;
                          });
                          HapticFeedback.lightImpact();
                          
                          // If pin is already placed, update it with new skin
                          if (_hasPlacedPin && pinPosition != null) {
                            _updatePinWithNewSkin();
                          }
                        },
                        child: AnimatedContainer(
                          duration: const Duration(milliseconds: 300),
                          width: 128,
                          margin: const EdgeInsets.symmetric(horizontal: 8),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: isSelected 
                                ? [
                                    theme.colorScheme.primary.withOpacity(0.6),
                                    theme.colorScheme.primary.withOpacity(0.3),
                                    theme.colorScheme.primary.withOpacity(0.1),
                                  ]
                                : gradientColors,
                            ),
                            borderRadius: BorderRadius.circular(20),
                            border: Border.all(
                              color: isSelected 
                                  ? theme.colorScheme.primary.withOpacity(0.8)
                                  : Colors.white.withOpacity(isDark ? 0.15 : 0.25),
                              width: isSelected ? 2.5 : 1,
                            ),
                            boxShadow: [
                              if (isSelected) 
                                BoxShadow(
                                  color: theme.colorScheme.primary.withOpacity(0.4),
                                  blurRadius: 20,
                                  offset: const Offset(0, 6),
                                  spreadRadius: 2,
                                ),
                              BoxShadow(
                                color: Colors.black.withOpacity(isDark ? 0.4 : 0.15),
                                blurRadius: 15,
                                offset: const Offset(0, 4),
                              ),
                            ],
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(20),
                            child: Stack(
                              children: [
                                // Background blur effect
                                Positioned.fill(
                                  child: Container(
                                    decoration: BoxDecoration(
                                      gradient: LinearGradient(
                                        begin: Alignment.topLeft,
                                        end: Alignment.bottomRight,
                                        colors: [
                                          Colors.white.withOpacity(isDark ? 0.05 : 0.1),
                                          Colors.white.withOpacity(isDark ? 0.02 : 0.05),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                                
                                // Content
                                Padding(
                                  padding: const EdgeInsets.all(12),
                                  child: Column(
                                    children: [
                                      // Pin preview with glow
                                      Expanded(
                                        child: Stack(
                                          children: [
                                            // Glow effect for selected
                                            if (isSelected)
                                              Container(
                                                width: 60,
                                                height: 70,
                                                decoration: BoxDecoration(
                                                  shape: BoxShape.circle,
                                                  boxShadow: [
                                                    BoxShadow(
                                                      color: theme.colorScheme.primary.withOpacity(0.6),
                                                      blurRadius: 25,
                                                      spreadRadius: 8,
                                                    ),
                                                  ],
                                                ),
                                              ),
                                                                                         // Pin image
                                             Center(
                                               child: Container(
                                                 width: 60,
                                                 height: 70,
                                                 decoration: BoxDecoration(
                                                   color: Colors.white.withOpacity(0.1),
                                                   borderRadius: BorderRadius.circular(12),
                                                   border: Border.all(
                                                     color: Colors.white.withOpacity(0.2),
                                                     width: 1,
                                                   ),
                                                 ),
                                                 child: ClipRRect(
                                                   borderRadius: BorderRadius.circular(11),
                                                   child: _buildPinImageWidget(
                                                     pinSkin.image,
                                                     isSelected ? 60 : 55,
                                                   ),
                                                 ),
                                               ),
                                             ),
                                          ],
                                        ),
                                      ),
                                      
                                      const SizedBox(height: 6),
                                      
                                      // Pin name
                                      Text(
                                        pinSkin.name,
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontSize: 12,
                                          fontWeight: FontWeight.w600,
                                          letterSpacing: 0.2,
                                        ),
                                        textAlign: TextAlign.center,
                                        maxLines: 2,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                      
                                      // Rarity badge
                                      if (pinSkin.skinType.isNotEmpty)
                                        Container(
                                          margin: const EdgeInsets.only(top: 4),
                                          padding: const EdgeInsets.symmetric(
                                            horizontal: 6,
                                            vertical: 2,
                                          ),
                                          decoration: BoxDecoration(
                                            color: _getRarityColor(pinSkin.skinType).withOpacity(0.2),
                                            borderRadius: BorderRadius.circular(8),
                                            border: Border.all(
                                              color: _getRarityColor(pinSkin.skinType).withOpacity(0.5),
                                              width: 0.5,
                                            ),
                                          ),
                                          child: Text(
                                            pinSkin.skinType,
                                            style: TextStyle(
                                              fontSize: 8,
                                              fontWeight: FontWeight.bold,
                                              color: _getRarityColor(pinSkin.skinType),
                                              letterSpacing: 0.3,
                                            ),
                                          ),
                                        ),
                                    ],
                                  ),
                                ),
                                
                                // Premium badge
                                if (pinSkin.isPremium)
                                  Positioned(
                                    top: 8,
                                    right: 8,
                                    child: Container(
                                      padding: const EdgeInsets.all(4),
                                      decoration: BoxDecoration(
                                        color: Colors.amber.withOpacity(0.2),
                                        borderRadius: BorderRadius.circular(8),
                                        border: Border.all(
                                          color: Colors.amber.withOpacity(0.5),
                                          width: 1,
                                        ),
                                      ),
                                      child: const Icon(
                                        Icons.star,
                                        color: Colors.amber,
                                        size: 12,
                                      ),
                                    ),
                                  ),
                                
                                // Selection indicator
                                if (isSelected)
                                  Positioned(
                                    bottom: 8,
                                    right: 8,
                                    child: Container(
                                      padding: const EdgeInsets.all(4),
                                      decoration: BoxDecoration(
                                        color: Colors.green.withOpacity(0.2),
                                        borderRadius: BorderRadius.circular(8),
                                        border: Border.all(
                                          color: Colors.green,
                                          width: 1,
                                        ),
                                      ),
                                      child: const Icon(
                                        Icons.check,
                                        color: Colors.green,
                                        size: 12,
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ),
                      );
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Helper methods for glassmorphism styling
  List<Color> _getCardGradient(int index, bool isDark) {
    final gradients = [
      [Colors.blue, Colors.indigo],
      [Colors.purple, Colors.deepPurple],
      [Colors.teal, Colors.cyan],
      [Colors.orange, Colors.deepOrange],
      [Colors.pink, Colors.red],
      [Colors.green, Colors.lightGreen],
    ];
    
    final colors = gradients[index % gradients.length];
    return [
      colors[0].withOpacity(isDark ? 0.4 : 0.3),
      colors[1].withOpacity(isDark ? 0.2 : 0.15),
      colors[0].withOpacity(isDark ? 0.1 : 0.05),
    ];
  }

  Color _getRarityColor(String skinType) {
    switch (skinType.toUpperCase()) {
      case 'ARTIST':
        return Colors.purple;
      case 'HOUSE':
        return Colors.blue;
      case 'PREMIUM':
        return Colors.amber;
      default:
        return Colors.grey;
    }
  }

  // Helper widget to build pin image (either network or asset)
  Widget _buildPinImageWidget(String? imageUrl, double size) {
    if (imageUrl == null || !_hasValidImageUrl(imageUrl)) {
      // Fallback to default pin icon
      return Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.2),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          Icons.location_on,
          color: Colors.white.withOpacity(0.8),
          size: size * 0.6,
        ),
      );
    }

    if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
      // Network image
      return Image.network(
        imageUrl,
        width: size,
        height: size,
        fit: BoxFit.cover,
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) return child;
          return Container(
            width: size,
            height: size,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Center(
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    Colors.white.withOpacity(0.7),
                  ),
                ),
              ),
            ),
          );
        },
        errorBuilder: (context, error, stackTrace) {
          // Fallback on error
          return Container(
            width: size,
            height: size,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.location_on,
              color: Colors.white.withOpacity(0.8),
              size: size * 0.6,
            ),
          );
        },
      );
    } else {
      // Asset image
      return Image.asset(
        imageUrl,
        width: size,
        height: size,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          // Fallback on error
          return Container(
            width: size,
            height: size,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.location_on,
              color: Colors.white.withOpacity(0.8),
              size: size * 0.6,
            ),
          );
        },
      );
    }
  }
  
  Widget _buildRadiusAdjustment(BuildContext context, ThemeData theme) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.8),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: theme.colorScheme.primary.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Pin Radius',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(15),
                ),
                child: Text(
                  '${_currentRadius.toStringAsFixed(0)}m',
                  style: TextStyle(
                    color: theme.colorScheme.primary,
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          // Custom styled slider
          SliderTheme(
            data: SliderTheme.of(context).copyWith(
              activeTrackColor: theme.colorScheme.primary,
              inactiveTrackColor: theme.colorScheme.primary.withOpacity(0.3),
              thumbColor: theme.colorScheme.primary,
              thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 12),
              overlayColor: theme.colorScheme.primary.withOpacity(0.2),
              overlayShape: const RoundSliderOverlayShape(overlayRadius: 24),
              trackHeight: 6,
            ),
            child: Slider(
              value: _currentRadius,
              min: 50.0,
              max: 100.0,
              divisions: 50,
              onChanged: (value) {
                setState(() {
                  _currentRadius = value;
                });
                _updateRadiusIndicator();
                HapticFeedback.selectionClick();
              },
            ),
          ),
          
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '50m',
                style: TextStyle(
                  color: Colors.white.withOpacity(0.5),
                  fontSize: 12,
                ),
              ),
              Expanded(
                child: Text(
                  'Adjust the area where your music can be discovered',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.7),
                    fontSize: 12,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              Text(
                '100m',
                style: TextStyle(
                  color: Colors.white.withOpacity(0.5),
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
  
  Widget _buildActionButtons(BuildContext context, ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
      child: Row(
        children: [
          // Cancel button
          Expanded(
            child: OutlinedButton(
              onPressed: () {
                HapticFeedback.lightImpact();
                _resetPin();
              },
              style: OutlinedButton.styleFrom(
                foregroundColor: Colors.white,
                side: const BorderSide(color: Colors.white),
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
              ),
              child: const Text(
                'Reposition',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
          
          const SizedBox(width: 16),
          
          // Save button
          Expanded(
            flex: 2,
            child: GradientButton(
              onPressed: _savePin,
              text: 'Drop Pin',
              icon: Icons.add_location_alt,
              gradient: LinearGradient(
                colors: [
                  theme.colorScheme.primary,
                  theme.colorScheme.secondary,
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  // ARKit Methods
  void _onARKitViewCreated(ARKitController controller) {
    arkitController = controller;
    
    // print('AR_DEBUG: _onARKitViewCreated called.');
    // print('📱 AR: ARKit view created, initializing with horizontal+vertical plane detection...');
    
    // Set up plane detection callbacks
    arkitController.onAddNodeForAnchor = _onPlaneAnchorAdded;
    arkitController.onUpdateNodeForAnchor = _onPlaneAnchorUpdated;
    arkitController.onARTap = _onSceneTapped;
    
    setState(() {
      _arInitialized = true;
    });
    
    // print('AR_DEBUG: ARKit initialized successfully. _arInitialized: $_arInitialized');
    // print('📱 AR: ARKit initialized successfully');
  }
  
  Future<void> _toggleFlash() async {
    try {
      if (_isFlashOn) {
        await TorchLight.disableTorch();
      } else {
        await TorchLight.enableTorch();
      }

      setState(() {
        _isFlashOn = !_isFlashOn;
      });

      // Provide haptic feedback
      HapticFeedback.lightImpact();

      // Show feedback to user
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                _isFlashOn ? Icons.flash_on : Icons.flash_off,
                color: Colors.white,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                _isFlashOn ? 'Flash On' : 'Flash Off',
                style: const TextStyle(color: Colors.white),
              ),
            ],
          ),
          backgroundColor: _isFlashOn ? Colors.amber.shade700 : Colors.grey.shade700,
          duration: const Duration(seconds: 1),
          behavior: SnackBarBehavior.floating,
          margin: const EdgeInsets.only(bottom: 100, left: 20, right: 20),
        ),
      );

    } on Exception catch (e) {
      print('Error toggling flash: $e');
      // Reset flash state on error
      setState(() {
        _isFlashOn = false;
      });
    }
  }
  
  void _onPlaneAnchorAdded(ARKitAnchor anchor) {
    // print('AR_DEBUG: _onPlaneAnchorAdded called.');
    if (anchor is! ARKitPlaneAnchor) {
      // print('AR_DEBUG: Anchor is not ARKitPlaneAnchor. Type: ${anchor.runtimeType}');
      // print('⚠️ AR: Anchor is not a plane anchor, ignoring');
      return;
    }
    
    final planeAnchor = anchor as ARKitPlaneAnchor;
    // print('AR_DEBUG: New ARKitPlaneAnchor detected. ID: ${planeAnchor.identifier}, Center: ${planeAnchor.center}, Extent: ${planeAnchor.extent}');
    // print('🆕 AR: New plane anchor detected: ${planeAnchor.identifier}');
    
    // Store the anchor
    detectedPlaneAnchors[planeAnchor.identifier] = planeAnchor;
    
    // Determine if floor or wall based on transform matrix
    final transform = planeAnchor.transform;
    final normal = _extractNormalFromTransform(transform.storage);
    final isFloor = _isHorizontalPlane(normal);
    
    // print('📐 AR: Plane normal: (${normal.x.toStringAsFixed(3)}, ${normal.y.toStringAsFixed(3)}, ${normal.z.toStringAsFixed(3)})');
    // print('📐 AR: Plane type: ${isFloor ? "FLOOR" : "WALL"}');
    
    planeOrientations[planeAnchor.identifier] = isFloor;
    
    // Provide haptic feedback for floor detection
    if (isFloor) {
      HapticFeedback.lightImpact();
      
      // Show feedback on first floor detection
      if (planeOrientations.values.where((isFloor) => isFloor).length == 1) {
        Future.microtask(() {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Row(
                  children: [
                    Icon(Icons.check_circle, color: Colors.white),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        '✅ Floor detected! Tap to place your pin',
                        style: TextStyle(color: Colors.white),
                      ),
                    ),
                  ],
                ),
                backgroundColor: Colors.green,
                duration: Duration(seconds: 2),
                behavior: SnackBarBehavior.floating,
              ),
            );
          }
        });
      }
    }
  }
  
  void _onPlaneAnchorUpdated(ARKitAnchor anchor) {
    // print('AR_DEBUG: _onPlaneAnchorUpdated called.');
    if (anchor is! ARKitPlaneAnchor) {
      // print('AR_DEBUG: Anchor is not ARKitPlaneAnchor during update. Type: ${anchor.runtimeType}');
      return;
    }
    
    final planeAnchor = anchor as ARKitPlaneAnchor;
    // print('AR_DEBUG: ARKitPlaneAnchor updated. ID: ${planeAnchor.identifier}, Center: ${planeAnchor.center}, Extent: ${planeAnchor.extent}');
    
    // Update stored anchor
    detectedPlaneAnchors[planeAnchor.identifier] = planeAnchor;
    
    // Update plane orientation if needed
    final transform = planeAnchor.transform;
    final normal = _extractNormalFromTransform(transform.storage);
    final isFloor = _isHorizontalPlane(normal);
    planeOrientations[planeAnchor.identifier] = isFloor;
  }
  
  void _onSceneTapped(List<ARKitTestResult> hits) {
    // print('👆 AR: Scene tapped with ${hits.length} hit results');
    
    if (_hasPlacedPin) {
      // print('📍 AR: Pin already placed, ignoring tap');
      return;
    }
    
    // Find the first hit on a horizontal plane (floor)
    ARKitTestResult? floorHit;
    
    for (final hit in hits) {
      // print('🎯 AR: Hit type: ${hit.type}, distance: ${hit.distance}');
      
      // Check if this hit is on an existing plane
      if (hit.type == ARKitHitTestResultType.existingPlaneUsingExtent ||
          hit.type == ARKitHitTestResultType.existingPlane) {
        
        // Get the transform and check if it's a horizontal plane
        final transform = hit.worldTransform;
        final normal = _extractNormalFromTransform(transform.storage);
        
        if (_isHorizontalPlane(normal)) {
          floorHit = hit;
          // print('✅ AR: Found floor hit at distance ${hit.distance}');
          break;
        } else {
          // print('🚫 AR: Hit is on a wall, skipping');
        }
      }
    }
    
    // If no floor hit found, try estimated horizontal plane
    if (floorHit == null) {
      for (final hit in hits) {
        if (hit.type == ARKitHitTestResultType.estimatedHorizontalPlane) {
          floorHit = hit;
          // print('✅ AR: Using estimated horizontal plane');
          break;
        }
      }
    }
    
    if (floorHit != null) {
      _placePin(floorHit);
    } else {
      // print('❌ AR: No floor surface found');
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please tap on a floor surface. Pins cannot be placed on walls.'),
          backgroundColor: Colors.orange,
          duration: Duration(seconds: 2),
        ),
      );
    }
  }
  
  vector.Vector3 _extractNormalFromTransform(List<double> transform) {
    // The transform is a 4x4 matrix in column-major order
    // The Y-axis (up vector) is in columns 4, 5, 6 (indices 4, 5, 6)
    return vector.Vector3(transform[4], transform[5], transform[6]);
  }
  
  bool _isHorizontalPlane(vector.Vector3 normal) {
    // A horizontal plane has a normal that points mostly in the Y direction
    // Check if Y component dominates (accounting for some tolerance)
    final absY = normal.y.abs();
    final absX = normal.x.abs();
    final absZ = normal.z.abs();
    
    // Horizontal if Y component is significantly larger than X and Z
    return absY > absX && absY > absZ && absY > 0.8; // 0.8 threshold for near-horizontal (more tolerant)
  }
  
  void _placePin(ARKitTestResult hit) {
    // print('📍 AR: Placing pin on floor...');
    
    setState(() {
      _isPlacingPin = true;
    });
    
    HapticFeedback.mediumImpact();
    
    // Get position from hit test
    final position = hit.worldTransform.getTranslation();
    pinPosition = position;
    
    // Calculate and log coordinate information for debugging
    if (widget.initialLatitude != null && widget.initialLongitude != null) {
      final offsetMeters = arPositionToRealWorldOffset(position);
      final pinCoordinates = calculateOffsetCoordinates(
        widget.initialLatitude!,
        widget.initialLongitude!,
        offsetMeters,
      );
      
      // Detailed logging for verification
      print('🎯 AR PIN PLACEMENT DEBUG:');
      print('  AR Position: (${position.x.toStringAsFixed(3)}, ${position.y.toStringAsFixed(3)}, ${position.z.toStringAsFixed(3)})');
      print('  User GPS: (${widget.initialLatitude!.toStringAsFixed(6)}, ${widget.initialLongitude!.toStringAsFixed(6)})');
      print('  Calculated Offset: E-W: ${offsetMeters.x.toStringAsFixed(2)}m, N-S: ${offsetMeters.y.toStringAsFixed(2)}m');
      print('  Pin GPS: (${pinCoordinates['latitude']!.toStringAsFixed(6)}, ${pinCoordinates['longitude']!.toStringAsFixed(6)})');
      
      final distance = calculateDistance(
        widget.initialLatitude!,
        widget.initialLongitude!,
        pinCoordinates['latitude']!,
        pinCoordinates['longitude']!,
      );
      print('  Real-world distance: ${distance.toStringAsFixed(2)}m');
    }
    
    // print('📍 AR: Pin position: (${position.x.toStringAsFixed(3)}, ${position.y.toStringAsFixed(3)}, ${position.z.toStringAsFixed(3)})');
    
    // Create pin visualization
    _createPinNode();
    
    // Add radius indicator
    _addRadiusIndicator();
    
    setState(() {
      _hasPlacedPin = true;
      _isPlacingPin = false;
    });
    
    // Animate radius UI
    _radiusAnimationController.forward();
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('📍 Pin placed on floor! Adjust radius and save.'),
        backgroundColor: Colors.green,
        duration: Duration(seconds: 2),
      ),
    );
  }
  
  Future<void> _createPinNode() async {
    if (pinPosition == null) return;

    // Create parent node at the tapped position
    pinNode = ARKitNode(position: pinPosition!);
    arkitController.add(pinNode!);

    // Define pin dimensions
    const double stemRadius = 0.01;
    const double stemHeight = 0.4;
    const double headRadius = 0.15;
    const double headThickness = 0.02;

    // Stem: thin cylinder
    final stemMaterial = ARKitMaterial(
      lightingModelName: ARKitLightingModel.constant,
      diffuse: ARKitMaterialProperty.color(Colors.grey),
    );
    final stemNode = ARKitNode(
      geometry: ARKitCylinder(
        radius: stemRadius,
        height: stemHeight,
        materials: [stemMaterial],
      ),
      // Position so bottom of stem sits at ground
      position: vector.Vector3(0, stemHeight / 2, 0),
    );
    arkitController.add(stemNode, parentNodeName: pinNode!.name);

    // Create initial head with loading shimmer effect
    final loadingMaterial = ARKitMaterial(
      lightingModelName: ARKitLightingModel.constant,
      diffuse: ARKitMaterialProperty.color(
        Theme.of(context).colorScheme.primary.withOpacity(0.5)
      ),
      emission: ARKitMaterialProperty.color(
        Theme.of(context).colorScheme.primary.withOpacity(0.3)
      ),
      doubleSided: true,
    );
    
    _headNode = ARKitNode(
      geometry: ARKitCylinder(
        radius: headRadius,
        height: headThickness,
        materials: [loadingMaterial],
      ),
      // Position on top of stem
      position: vector.Vector3(0, stemHeight + headThickness / 2, 0),
      // Set rotation once to face the user  
      eulerAngles: vector.Vector3(0, math.pi / 2, 0),
    );
    arkitController.add(_headNode!, parentNodeName: pinNode!.name);

    // Load and rotate image in the background
    _loadAndApplyRotatedTexture();
  }

  Future<void> _loadAndApplyRotatedTexture() async {
    try {
      // Get the selected skin's image URL
      final selectedSkin = _availableSkins.isNotEmpty 
          ? _availableSkins[_selectedPinSkinIndex]
          : null;
      
      final pinImageUrl = selectedSkin?.image ?? _pinImageUrl;
      
      if (!_hasValidImageUrl(pinImageUrl)) {
        // If no valid image, keep the loading color
        return;
      }

      // Load and rotate the image
      final rotatedImageData = await _loadAndRotateImage(pinImageUrl);
      
      if (rotatedImageData != null && _headNode != null) {
        // Update the head material with rotated image
        await _updateHeadMaterial(rotatedImageData);
      }
    } catch (e) {
      print('Error loading pin texture: $e');
      // Keep the loading color as fallback
    }
  }

  Future<String?> _loadAndRotateImage(String imageUrl) async {
    try {
      Uint8List imageBytes;
      
      if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
        // Load network image
        final response = await Dio().get(
          imageUrl,
          options: Options(responseType: ResponseType.bytes),
        );
        imageBytes = Uint8List.fromList(response.data);
      } else if (imageUrl.startsWith('assets/')) {
        // Load asset image
        final ByteData data = await rootBundle.load(imageUrl);
        imageBytes = data.buffer.asUint8List();
      } else {
        return null;
      }

      // Decode the image
      final image = img.decodeImage(imageBytes);
      if (image == null) return null;

      // Rotate the image 90 degrees clockwise to fix orientation
      final rotatedImage = img.copyRotate(image, angle: 90);

      // Encode back to PNG
      final pngBytes = img.encodePng(rotatedImage);
      
      // Convert to base64 data URL
      final base64String = base64Encode(pngBytes);
      return 'data:image/png;base64,$base64String';
      
    } catch (e) {
      print('Error processing image: $e');
      return null;
    }
  }

  Future<void> _updateHeadMaterial(String rotatedImageData) async {
    if (_headNode == null || pinNode == null) return;

    try {
      // Remove old head node
      arkitController.remove(_headNode!.name);
      
      // Create new head with rotated image starting transparent
      const double stemHeight = 0.4;
      const double headRadius = 0.15;
      const double headThickness = 0.02;
      
      final headMaterial = ARKitMaterial(
        lightingModelName: ARKitLightingModel.constant,
        diffuse: ARKitMaterialProperty.image(rotatedImageData),
        transparency: 0.0, // Start completely transparent
        doubleSided: true,
      );
      
      _headNode = ARKitNode(
        geometry: ARKitCylinder(
          radius: headRadius,
          height: headThickness,
          materials: [headMaterial],
        ),
        position: vector.Vector3(0, stemHeight + headThickness / 2, 0),
        eulerAngles: vector.Vector3(0, math.pi / 2, 0),
      );
      
      arkitController.add(_headNode!, parentNodeName: pinNode!.name);
      
      // Start fade-in animation
      _animateFadeIn(rotatedImageData);
      
    } catch (e) {
      print('Error updating head material: $e');
    }
  }

  void _animateFadeIn(String rotatedImageData) {
    const animationDuration = Duration(milliseconds: 800);
    const frameRate = 60; // 60 FPS
    final totalFrames = (animationDuration.inMilliseconds * frameRate / 1000).round();
    
    Timer.periodic(Duration(milliseconds: (1000 / frameRate).round()), (timer) {
      if (_headNode == null || pinNode == null) {
        timer.cancel();
        return;
      }
      
      final progress = timer.tick / totalFrames;
      
      if (progress >= 1.0) {
        timer.cancel();
        // Ensure final state is fully opaque
        _updateNodeTransparency(rotatedImageData, 1.0);
        return;
      }
      
      // Use easeOut curve for smooth animation
      final easedProgress = _easeOutCubic(progress);
      _updateNodeTransparency(rotatedImageData, easedProgress);
    });
  }

  void _updateNodeTransparency(String rotatedImageData, double opacity) {
    if (_headNode == null || pinNode == null) return;
    
    try {
      // Remove current head
      arkitController.remove(_headNode!.name);
      
      // Create new head with updated transparency
      const double stemHeight = 0.4;
      const double headRadius = 0.15;
      const double headThickness = 0.02;
      
      final headMaterial = ARKitMaterial(
        lightingModelName: ARKitLightingModel.constant,
        diffuse: ARKitMaterialProperty.image(rotatedImageData),
        transparency: opacity,
        doubleSided: true,
      );
      
      _headNode = ARKitNode(
        geometry: ARKitCylinder(
          radius: headRadius,
          height: headThickness,
          materials: [headMaterial],
        ),
        position: vector.Vector3(0, stemHeight + headThickness / 2, 0),
        eulerAngles: vector.Vector3(0, math.pi / 2, 0),
      );
      
      arkitController.add(_headNode!, parentNodeName: pinNode!.name);
    } catch (e) {
      print('Error updating transparency: $e');
    }
  }

  // Easing function for smooth animation
  double _easeOutCubic(double t) {
    return 1 - math.pow(1 - t, 3).toDouble();
  }
  
  void _addRadiusIndicator() {
    if (pinPosition == null) return;
    
    // Remove any existing sonar rings
    _clearSonarRings();
    
    // Create the main radius indicator (static ring)
    final mainMaterial = ARKitMaterial(
      lightingModelName: ARKitLightingModel.constant,
      diffuse: ARKitMaterialProperty.color(
        Theme.of(context).colorScheme.primary.withOpacity(0.6),
      ),
      transparency: 0.4,
    );
    
    radiusNode = ARKitNode(
      geometry: ARKitTorus(
        ringRadius: _currentRadius / 10, // Scale for AR
        pipeRadius: 0.015,
        materials: [mainMaterial],
      ),
      position: pinPosition! + vector.Vector3(0, 0.01, 0),
      eulerAngles: vector.Vector3(-math.pi / 2, 0, 0),
    );
    
    arkitController.add(radiusNode!);
    
    // Start sonar animation
    _startSonarAnimation();
  }
  
  void _addCompassIndicator() {
    if (pinPosition == null) return;
    
    // Calculate real-world offset
    final offsetMeters = arPositionToRealWorldOffset(pinPosition!);
    
    // Only show compass indicator if pin is placed far from user (> 5 meters)
    final distance = math.sqrt(offsetMeters.x * offsetMeters.x + offsetMeters.y * offsetMeters.y);
    
    if (distance > 5.0) {
      // Calculate angle from user to pin (0° = North)
      final angle = math.atan2(offsetMeters.x, offsetMeters.y);
      
      // Create arrow pointing to real-world direction
      final arrowMaterial = ARKitMaterial(
        lightingModelName: ARKitLightingModel.constant,
        diffuse: ARKitMaterialProperty.color(Colors.white.withOpacity(0.6)),
      );
      
      // Create a simple arrow shape using a cone
      final arrowNode = ARKitNode(
        geometry: ARKitCone(
          topRadius: 0,
          bottomRadius: 0.02,
          height: 0.08,
          materials: [arrowMaterial],
        ),
        position: pinPosition! + vector.Vector3(0, 0.4, 0),
        eulerAngles: vector.Vector3(0, angle, -math.pi / 2),
      );
      
      arkitController.add(arrowNode);
    }
    
    debugPrint('📏 AR: Pin distance indicator: ${distance.toStringAsFixed(1)}m');
  }
  
  void _startSonarAnimation() {
    _sonarTimer?.cancel();
    
    // Create a new sonar ring every 1200ms (even slower)
    _sonarTimer = Timer.periodic(const Duration(milliseconds: 1200), (timer) {
      _createSonarRing();
    });
  }
  
  void _createSonarRing() {
    if (pinPosition == null) return;
    
    // Store theme color at creation time
    final themeColor = Theme.of(context).colorScheme.primary;
    
    // Create material with animated opacity and color
    final sonarMaterial = ARKitMaterial(
      lightingModelName: ARKitLightingModel.constant,
      diffuse: ARKitMaterialProperty.color(
        themeColor.withOpacity(0.9),
      ),
      transparency: 0.1,
    );
    
    // Start from the pin center (radius 0.0)
    final initialRadius = 0.0; // Start from pin center
    final maxRadius = _currentRadius / 10; // End at pin radius
    
    final sonarRing = ARKitNode(
      geometry: ARKitTorus(
        ringRadius: initialRadius + 0.01, // Add tiny amount to make it visible initially
        pipeRadius: 0.005, // Thinner than main ring
        materials: [sonarMaterial],
      ),
      position: pinPosition! + vector.Vector3(0, 0.005, 0), // Slightly below main ring
      eulerAngles: vector.Vector3(-math.pi / 2, 0, 0),
    );
    
    arkitController.add(sonarRing);
    _sonarRings.add(sonarRing);
    
    // Animate the ring expansion and fade out
    _animateSonarRing(sonarRing, initialRadius, maxRadius, themeColor);
  }
  
  void _animateSonarRing(ARKitNode ring, double startRadius, double endRadius, Color themeColor) {
    // Create a local animation controller for this ring (even longer duration)
    final animationController = AnimationController(
      duration: const Duration(milliseconds: 3000), // Even slower animation (was 2000ms)
      vsync: this,
    );
    
    final radiusAnimation = Tween<double>(
      begin: startRadius,
      end: endRadius,
    ).animate(CurvedAnimation(
      parent: animationController,
      curve: Curves.easeOut,
    ));
    
    final opacityAnimation = Tween<double>(
      begin: 0.9, // Start more visible
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: animationController,
      curve: const Interval(0.2, 1.0, curve: Curves.easeOut), // Start fading earlier but more gradually
    ));
    
    ARKitNode? currentRing = ring;
    
    animationController.addListener(() {
      if (!mounted || currentRing == null) {
        animationController.dispose();
        return;
      }
      
      try {
        // Update ring size and opacity
        final newRadius = radiusAnimation.value;
        final opacity = opacityAnimation.value;
        
        // Remove the old ring
        arkitController.remove(currentRing!.name);
        
        // Create new material with updated opacity using stored theme color
        final updatedMaterial = ARKitMaterial(
          lightingModelName: ARKitLightingModel.constant,
          diffuse: ARKitMaterialProperty.color(
            themeColor.withOpacity(opacity),
          ),
          transparency: 1.0 - opacity,
        );
        
        // Create new ring with updated geometry
        currentRing = ARKitNode(
          geometry: ARKitTorus(
            ringRadius: math.max(newRadius, 0.01),
            pipeRadius: 0.005,
            materials: [updatedMaterial],
          ),
          position: pinPosition! + vector.Vector3(0, 0.005, 0),
          eulerAngles: vector.Vector3(-math.pi / 2, 0, 0),
        );
        
        arkitController.add(currentRing!);
      } catch (e) {
        // Handle errors silently in production
        // print('Error updating sonar ring: $e');
      }
    });
    
    animationController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        try {
          if (currentRing != null) {
            arkitController.remove(currentRing!.name);
            _sonarRings.remove(ring);
          }
        } catch (e) {
          // Handle errors silently in production
          // print('Error removing sonar ring: $e');
        }
        animationController.dispose();
      }
    });
    
    // Start the animation
    animationController.forward();
  }
  
  void _clearSonarRings() {
    for (final ring in _sonarRings) {
      try {
        arkitController.remove(ring.name);
      } catch (e) {
        // Ring may already be removed
      }
    }
    _sonarRings.clear();
    _sonarTimer?.cancel();
  }
  
  void _updateRadiusIndicator() {
    if (radiusNode != null) {
      arkitController.remove(radiusNode!.name);
      _clearSonarRings(); // Clear any existing sonar animation
      _addRadiusIndicator();
    }
  }
  
  void _resetPin() {
    _clearSonarRings(); // Clear sonar animation
    
    if (pinNode != null) {
      arkitController.remove(pinNode!.name);
    }
    if (radiusNode != null) {
      arkitController.remove(radiusNode!.name);
    }
    
    setState(() {
      _hasPlacedPin = false;
      pinNode = null;
      radiusNode = null;
      _headNode = null;
      pinPosition = null;
    });
    
    _radiusAnimationController.reset();
  }

  Future<void> _updatePinWithNewSkin() async {
    if (pinNode == null || _headNode == null) return;
    
    try {
      // Remove old head node
      arkitController.remove(_headNode!.name);
      
      // Create loading head first for immediate feedback
      const double stemHeight = 0.4;
      const double headRadius = 0.15;
      const double headThickness = 0.02;
      
      final loadingMaterial = ARKitMaterial(
        lightingModelName: ARKitLightingModel.constant,
        diffuse: ARKitMaterialProperty.color(
          Theme.of(context).colorScheme.primary.withOpacity(0.5)
        ),
        emission: ARKitMaterialProperty.color(
          Theme.of(context).colorScheme.primary.withOpacity(0.3)
        ),
        doubleSided: true,
      );
      
      _headNode = ARKitNode(
        geometry: ARKitCylinder(
          radius: headRadius,
          height: headThickness,
          materials: [loadingMaterial],
        ),
        position: vector.Vector3(0, stemHeight + headThickness / 2, 0),
        eulerAngles: vector.Vector3(0, math.pi / 2, 0),
      );
      
      arkitController.add(_headNode!, parentNodeName: pinNode!.name);
      
      // Load and apply new skin in background
      await _loadAndApplyRotatedTexture();
      
    } catch (e) {
      print('Error updating pin skin: $e');
    }
  }
  
  /// Ensures we have a valid artwork URL or provides a fallback
  String _getValidArtworkUrl(String? artworkUrl) {
    if (artworkUrl != null && artworkUrl.isNotEmpty && artworkUrl.startsWith('http')) {
      return artworkUrl;
    }
    // Provide a fallback image URL if no valid artwork URL is available
    return 'https://via.placeholder.com/300x300.png?text=No+Artwork';
  }

  /// Shows a modern bottomsheet for caption input
  Future<String?> _showCaptionInput() async {
    final textController = TextEditingController();
    final defaultCaption = widget.selectedTrack?.title != null 
        ? 'Check out ${widget.selectedTrack!.title}!' 
        : 'Check out this song!';
    
    // Set text immediately for faster display
    textController.text = defaultCaption;
    
    return showModalBottomSheet<String>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      useRootNavigator: false,
      isDismissible: true,
      enableDrag: true,
      builder: (context) => CaptionInputBottomSheet(
        selectedTrack: widget.selectedTrack,
        controller: textController,
        defaultCaption: defaultCaption,
      ),
    );
  }

  /// Gets a specific location name using reverse geocoding
  Future<String> _getLocationName(double latitude, double longitude) async {
    try {
      final placemarks = await placemarkFromCoordinates(latitude, longitude);
      
      if (placemarks.isNotEmpty) {
        final placemark = placemarks.first;
        
        // Debug logging to see what data is available
        debugPrint('🗺️ Geocoding data for ($latitude, $longitude):');
        debugPrint('   name: "${placemark.name}"');
        debugPrint('   street: "${placemark.street}"');
        debugPrint('   subThoroughfare: "${placemark.subThoroughfare}"');
        debugPrint('   thoroughfare: "${placemark.thoroughfare}"');
        debugPrint('   locality: "${placemark.locality}"');
        debugPrint('   subLocality: "${placemark.subLocality}"');
        debugPrint('   administrativeArea: "${placemark.administrativeArea}"');
        debugPrint('   subAdministrativeArea: "${placemark.subAdministrativeArea}"');
        debugPrint('   postalCode: "${placemark.postalCode}"');
        debugPrint('   country: "${placemark.country}"');
        
        // Build a specific location name from available components
        List<String> locationParts = [];
        
        // Add specific building/establishment name if available
        if (placemark.name != null && placemark.name!.isNotEmpty && 
            placemark.name! != placemark.locality &&
            !placemark.name!.contains(RegExp(r'^\d+'))) { // Skip if name starts with number (likely an address)
          locationParts.add(placemark.name!);
        }
        
        // Add locality (city/town) if available and different from name
        if (placemark.locality != null && placemark.locality!.isNotEmpty &&
            !locationParts.any((part) => part.toLowerCase().contains(placemark.locality!.toLowerCase()))) {
          locationParts.add(placemark.locality!);
        }
        
        // Only add state if we just have name without city
        if (locationParts.length == 1 && placemark.administrativeArea != null && 
            placemark.administrativeArea!.isNotEmpty) {
          locationParts.add(placemark.administrativeArea!);
        }
        
        // Join the parts with commas - should typically be "Name, City"
        final locationName = locationParts.join(', ');
        
        if (locationName.isNotEmpty) {
          return locationName;
        }
      }
      
      // Fallback to coordinates if no meaningful location found
      return 'Location (${latitude.toStringAsFixed(4)}, ${longitude.toStringAsFixed(4)})';
    } catch (e) {
      debugPrint('Error getting location name: $e');
      return 'Unknown Location';
    }
  }

  Future<void> _savePin() async {
    if (pinPosition == null || widget.initialLatitude == null || widget.initialLongitude == null) {
      print('Error: Cannot save pin - missing position data');
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Error: Could not determine pin position'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // Update position right before saving to get most accurate altitude
    await _getCurrentPosition();

    // Show caption input bottomsheet first
    final customCaption = await _showCaptionInput();
    if (customCaption == null) {
      // User cancelled
      return;
    }

    // 🚀 OPTIMISTIC APPROACH: Show success immediately and close screen
    HapticFeedback.heavyImpact();
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('🎉 Pin dropped successfully!'),
        backgroundColor: Colors.green,
        duration: Duration(seconds: 2),
      ),
    );

    try {
      print('Starting pin save process...');
      print('AR Pin Position: (${pinPosition!.x.toStringAsFixed(3)}, ${pinPosition!.y.toStringAsFixed(3)}, ${pinPosition!.z.toStringAsFixed(3)})');
      print('User Location: Lat: ${widget.initialLatitude}, Lng: ${widget.initialLongitude}');
      print('Radius: ${_currentRadius}m');

      // Calculate real-world offset from AR position
      final offsetMeters = arPositionToRealWorldOffset(pinPosition!);
      
      // Calculate actual pin coordinates based on offset
      final pinCoordinates = calculateOffsetCoordinates(
        widget.initialLatitude!,
        widget.initialLongitude!,
        offsetMeters,
      );
      
      // Validate the calculated coordinates
      if (!validateCoordinates(
        pinCoordinates['latitude']!, 
        pinCoordinates['longitude']!,
        widget.initialLatitude!,
        widget.initialLongitude!
      )) {
        throw Exception('Invalid pin coordinates calculated');
      }
      
      // Calculate distance for user feedback
      final pinDistance = calculateDistance(
        widget.initialLatitude!,
        widget.initialLongitude!,
        pinCoordinates['latitude']!,
        pinCoordinates['longitude']!,
      );
      
      print('📍 Pin placement distance: ${pinDistance.toStringAsFixed(2)}m from user position');

      // Get location name for the calculated pin position
      final locationName = await _getLocationName(
        pinCoordinates['latitude']!,
        pinCoordinates['longitude']!,
      );

      // Get the selected skin
      final selectedSkin = _availableSkins.isNotEmpty 
          ? _availableSkins[_selectedPinSkinIndex]
          : null;

      final genre = _cachedGenre;
      print('Genre: $genre');

      // Prepare data for API with calculated coordinates
      final pinData = {
        'location': {
          'type': 'Point',
          'coordinates': [pinCoordinates['longitude']!, pinCoordinates['latitude']!]
        },
        'ar_position': {
          'x': pinPosition!.x,
          'y': pinPosition!.y,
          'z': pinPosition!.z
        },
        'altitude': _currentPosition?.altitude ?? 0.0, // Use actual phone altitude
        'title': widget.selectedTrack?.title ?? 'My Music Pin',  // Use track title as pin title
        'track_title': widget.selectedTrack?.title ?? '',
        'track_artist': widget.selectedTrack?.artist ?? '',
        'track_url': widget.selectedTrack?.uri != null 
            ? 'https://open.spotify.com/track/${widget.selectedTrack!.uri.split(':').last}'
            : '',
        'service': 'spotify',
        'skin': selectedSkin?.id ?? 1,  // Use selected skin ID or fallback to 1
        'aura_radius': _currentRadius.round(),
        'is_private': false,
        'album': widget.selectedTrack?.album ?? '',
        'caption': customCaption,
        'location_name': locationName,
        'artwork_url': _getValidArtworkUrl(widget.selectedTrack?.albumArt),
        'duration_ms': widget.selectedTrack?.durationMs ?? 0,
        'genre': _cachedGenre, // Include pre-fetched genre
      };
      
      // Log genre status
      if (_cachedGenre != null) {
        debugPrint('🎯 [AR] Using cached genre: $_cachedGenre');
      } else if (_isLookingUpGenre) {
        debugPrint('⏳ [AR] Genre lookup still in progress, saving without genre');
      } else {
        debugPrint('❌ [AR] No genre found for ${widget.selectedTrack?.artist}');
      }

      print('Prepared pin data for API: $pinData');
      
      // 🚀 IMMEDIATE OPTIMISTIC DISPLAY: Show pin on map instantly before API call
      debugPrint('🔴 [AR-DEBUG] === ATTEMPTING OPTIMISTIC PIN DISPLAY ===');
      debugPrint('🔴 [AR-DEBUG] Mounted: $mounted');

      // 🔧 CRITICAL FIX: Add missing owner and skin details for proper rendering
      final optimisticPinData = Map<String, dynamic>.from(pinData);
      
      if (mounted) {
        debugPrint('🔴 [AR-DEBUG] Getting providers...');
        final mapProvider = Provider.of<MapProvider>(context, listen: false);
        final userProvider = Provider.of<UserProvider>(context, listen: false);
        
        debugPrint('🔴 [AR-DEBUG] MapProvider obtained: $mapProvider');
        debugPrint('🔴 [AR-DEBUG] MapProvider hashCode: ${mapProvider.hashCode}');
        
        // Add current user as pin owner (matches _createIndividualPinsLayer expectations)
        final currentUser = userProvider.currentUser;
        optimisticPinData['owner'] = {
          'username': currentUser?.username ?? 'Unknown',
          'name': currentUser?.username ?? 'Unknown', 
          'profile_pic': currentUser?.profilePicUrl,
        };
        
        // Add skin details with image URL (not just skin ID)
        if (selectedSkin != null) {
          optimisticPinData['skinDetails'] = {
            'image': selectedSkin.image,
            'name': selectedSkin.name,
          };
        }
        
        // Add optimistic flag for identification and temporary ID
        optimisticPinData['isOptimistic'] = true;
        optimisticPinData['id'] = 'temp_${DateTime.now().millisecondsSinceEpoch}'; // Temporary ID
        
        // 🔧 CRITICAL FIX: Add missing numeric fields to prevent type casting errors
        optimisticPinData['upvote_count'] = 0; // int, not String
        optimisticPinData['downvote_count'] = 0; // int, not String
        optimisticPinData['play_count'] = 0; // int, not String
        optimisticPinData['creator_id'] = currentUser?.id ?? 0; // int, not String
        optimisticPinData['created_at'] = DateTime.now().toIso8601String();
        
        // 🔧 CRITICAL FIX: Include caption for map screen display
        optimisticPinData['caption'] = customCaption; // Include user's caption
        optimisticPinData['description'] = customCaption; // Fallback field
        
        debugPrint('🔴 [AR-DEBUG] Enhanced pin data for optimistic display:');
        debugPrint('🔴 [AR-DEBUG] - Location: ${optimisticPinData['location']}');
        debugPrint('🔴 [AR-DEBUG] - Title: ${optimisticPinData['title']}');
        debugPrint('🔴 [AR-DEBUG] - Owner: ${optimisticPinData['owner']}');
        debugPrint('🔴 [AR-DEBUG] - SkinDetails: ${optimisticPinData['skinDetails']}');
        debugPrint('🔴 [AR-DEBUG] - Numeric fields: upvote=${optimisticPinData['upvote_count']}, downvote=${optimisticPinData['downvote_count']}');
        
        // 🚀 DISPLAY PIN IMMEDIATELY - No waiting for API!
        await _displayPinOptimisticallyWithRetry(mapProvider, optimisticPinData);
        debugPrint('🎯 [AR] Pin displayed optimistically IMMEDIATELY!');
        
        // Close the AR screen immediately - no loading overlay!
        Navigator.pop(context, true);
      } else {
        debugPrint('🛑 [AR-DEBUG] Widget not mounted - skipping optimistic display');
      }
      
      debugPrint('🔴 [AR-DEBUG] === OPTIMISTIC PIN DISPLAY COMPLETE ===');

      // 🔄 BACKGROUND API CALL: Save to backend in background (don't block UI)
      _saveToBackgroundAPI(pinData, optimisticPinData);
      
    } catch (e, stackTrace) {
      print('Error in optimistic pin creation: $e');
      print('Stack trace: $stackTrace');
      
      // If optimistic display fails, show error and don't proceed
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to create pin: ${e.toString()}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  /// Display pin optimistically with retry mechanism for race conditions
  Future<void> _displayPinOptimisticallyWithRetry(MapProvider mapProvider, Map<String, dynamic> optimisticPinData) async {
    const maxRetries = 10;
    const retryDelay = Duration(milliseconds: 50);

    for (int attempt = 1; attempt <= maxRetries; attempt++) {
      debugPrint('🔄 [AR-RETRY] Attempt $attempt/$maxRetries to display pin optimistically');
      debugPrint('🔄 [AR-RETRY] Callback registered: ${mapProvider.hasOptimisticPinCallback}');

      // Check if callback is registered
      if (mapProvider.hasOptimisticPinCallback) {
        debugPrint('🔄 [AR-RETRY] ✅ Callback found! Displaying pin...');
        await mapProvider.displayPinOptimistically(optimisticPinData);
        debugPrint('🔄 [AR-RETRY] ✅ Pin display completed successfully');
        return; // Success! Exit early
      }

      // Callback not registered yet, wait and retry
      if (attempt < maxRetries) {
        debugPrint('🔄 [AR-RETRY] ⏳ Callback not ready, waiting ${retryDelay.inMilliseconds}ms before retry...');
        await Future.delayed(retryDelay);
      } else {
        debugPrint('🔄 [AR-RETRY] ❌ Max retries reached, attempting final display anyway...');
        await mapProvider.displayPinOptimistically(optimisticPinData);
      }
    }
  }

  // 🔄 Background API call - happens after user sees the pin
  Future<void> _saveToBackgroundAPI(Map<String, dynamic> pinData, Map<String, dynamic> optimisticPinData) async {
    try {
      // Get auth token
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final token = authProvider.token;

      if (token == null) {
        throw Exception('Authentication token is missing');
      }

      // Construct and validate API URL
      final baseUrl = AppConstants.baseApiUrl.trim();
      final apiUrl = baseUrl.endsWith('/') 
          ? '${baseUrl}pins/' 
          : '$baseUrl/pins/';
      
      print('Making background API request to $apiUrl');

      // Make API call with proper headers
      final dio = Dio();
      
      // Configure Dio for better error handling
      dio.options.validateStatus = (status) => status != null && status < 500;
      dio.options.receiveTimeout = const Duration(seconds: 30);
      dio.options.sendTimeout = const Duration(seconds: 30);
      
      print('Request Headers: {Authorization: Bearer ${token.substring(0, 10)}..., Content-Type: application/json, Accept: application/json}');
      
      final response = await dio.post(
        apiUrl,
        data: pinData,
        options: Options(
          headers: {
            'Authorization': 'Bearer $token',
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
        ),
      );

      print('Background API Response Status: ${response.statusCode}');
      
      if (response.statusCode == 201) {
        print('Pin saved successfully in background! Pin ID: ${response.data['id']}');

        if (mounted) {
          // Get map provider to refresh and replace optimistic pin with real one
          final mapProvider = Provider.of<MapProvider>(context, listen: false);

          // Update optimistic pin with real ID
          optimisticPinData['id'] = response.data['id'];
          optimisticPinData.remove('isOptimistic'); // Remove optimistic flag

          debugPrint('🔴 [AR-DEBUG] Replacing optimistic pin with real data');
          
          // Trigger force refresh to fetch the new pin from backend with all correct data
          await mapProvider.forceRefreshPins();
          
          debugPrint('📍 [AR] Background API save complete - map refreshed');
          
          // 🚀 BACKGROUND GAMIFICATION: Track pin creation
          unawaited(GamificationIntegrationService.trackPinCreation(
            context,
            response.data['id'].toString(),
            pinData,
          ));
          
          debugPrint('🎮 [AR] Gamification tracking started in background');
        }
      } else {
        print('Background API call failed with status: ${response.statusCode}');
        // Note: We don't show error to user since they already think the pin was created
        // The optimistic pin will remain until app restart or manual refresh
      }
      
    } catch (e, stackTrace) {
      print('Background API save failed: $e');
      print('Stack trace: $stackTrace');
      // Note: We don't show error to user since they already think the pin was created
      // The optimistic pin will remain until app restart or manual refresh
    }
  }
} 

// New Stateful Widget for Pin Limit Reached Dialog
class PinLimitReachedDialog extends StatefulWidget {
  final DateTime? nextAvailableTime;

  const PinLimitReachedDialog({Key? key, required this.nextAvailableTime}) : super(key: key);

  @override
  State<PinLimitReachedDialog> createState() => _PinLimitReachedDialogState();
}

class _PinLimitReachedDialogState extends State<PinLimitReachedDialog> {
  Timer? _countdownTimer;
  String _timeRemaining = '';

  @override
  void initState() {
    super.initState();
    _startCountdown();
  }

  @override
  void dispose() {
    _countdownTimer?.cancel();
    super.dispose();
  }

  void _startCountdown() {
    if (widget.nextAvailableTime == null) {
      _timeRemaining = 'N/A';
      return;
    }

    _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      final now = DateTime.now();
      final duration = widget.nextAvailableTime!.difference(now);

      if (duration.isNegative) {
        setState(() {
          _timeRemaining = 'Now!';
        });
        timer.cancel();
      } else {
        setState(() {
          _timeRemaining = _formatDuration(duration);
        });
      }
    });
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final hours = twoDigits(duration.inHours);
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '${hours}h ${minutes}m ${seconds}s';
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: const EdgeInsets.all(20),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.white.withOpacity(0.2),
              Colors.white.withOpacity(0.1),
            ],
          ),
          border: Border.all(
            color: Colors.white.withOpacity(0.3),
            width: 1.5,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(20),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
            child: Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: Colors.transparent,
                borderRadius: BorderRadius.circular(20),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Warning Icon with Animated Pulse
                  TweenAnimationBuilder<double>(
                    duration: const Duration(milliseconds: 800),
                    tween: Tween(begin: 1.0, end: 1.2),
                    curve: Curves.easeInOut,
                    builder: (context, scale, child) {
                      return Transform.scale(
                        scale: scale,
                        child: Icon(
                          Icons.lock_clock_outlined,
                          color: Colors.red.shade600,
                          size: 64,
                        ),
                      );
                    },
                  ),
                  const SizedBox(height: 16),
                  
                  // Title
                  Text(
                    'Pin Limit Reached',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 22,
                      fontWeight: FontWeight.w700,
                      letterSpacing: 0.5,
                      shadows: [
                        Shadow(
                          blurRadius: 10.0,
                          color: Colors.black.withOpacity(0.3),
                          offset: const Offset(0, 3),
                        ),
                      ],
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  
                  // Message
                  Text(
                    'You have already dropped 2 pins in the last 24 hours. You can drop new pins in:',
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.9),
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      height: 1.5,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  
                  // Countdown Timer
                  Text(
                    _timeRemaining,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      fontFeatures: [FontFeature.tabularFigures(),],
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  
                  // Go Back Button
                  ElevatedButton(
                    onPressed: () {
                      Navigator.of(context).pop(); // Dismiss dialog
                      Navigator.pop(context); // Go back to previous screen
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).colorScheme.primary.withOpacity(0.8),
                      padding: const EdgeInsets.symmetric(horizontal: 40, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(30),
                      ),
                      elevation: 5,
                    ),
                    child: const Text(
                      'Go Back',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
} 